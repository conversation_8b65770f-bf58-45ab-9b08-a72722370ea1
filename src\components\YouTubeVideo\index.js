"use client";
import { useState } from "react";
import styles from "./styles.module.css";

const YouTubeVideo = ({ 
  videoId, 
  title = "YouTube Video", 
  width = "100%", 
  height = "315",
  className = "",
  showThumbnail = true,
  autoplay = false,
  controls = true,
  modestbranding = true,
  rel = false
}) => {
  const [isLoaded, setIsLoaded] = useState(!showThumbnail);

  // Extract video ID from various YouTube URL formats
  const extractVideoId = (url) => {
    if (!url) return null;
    
    // If it's already just an ID
    if (url.length === 11 && !url.includes('/')) {
      return url;
    }
    
    const patterns = [
      /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/,
      /youtube\.com\/v\/([^&\n?#]+)/,
      /youtube\.com\/watch\?.*v=([^&\n?#]+)/
    ];
    
    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match) return match[1];
    }
    
    return url; // Return as-is if no pattern matches
  };

  const finalVideoId = extractVideoId(videoId);
  
  if (!finalVideoId) {
    return (
      <div className={`${styles.errorContainer} ${className}`}>
        <p>Invalid YouTube video ID or URL</p>
      </div>
    );
  }

  const thumbnailUrl = `https://img.youtube.com/vi/${finalVideoId}/maxresdefault.jpg`;
  
  const embedUrl = `https://www.youtube.com/embed/${finalVideoId}?${new URLSearchParams({
    autoplay: autoplay ? '1' : '0',
    controls: controls ? '1' : '0',
    modestbranding: modestbranding ? '1' : '0',
    rel: rel ? '1' : '0',
    enablejsapi: '1',
    origin: typeof window !== 'undefined' ? window.location.origin : ''
  }).toString()}`;

  const handleThumbnailClick = () => {
    setIsLoaded(true);
  };

  return (
    <div 
      className={`${styles.videoContainer} ${className}`}
      style={{ width, height }}
    >
      {!isLoaded && showThumbnail ? (
        <div className={styles.thumbnailContainer} onClick={handleThumbnailClick}>
          <img 
            src={thumbnailUrl} 
            alt={title}
            className={styles.thumbnail}
            loading="lazy"
          />
          <div className={styles.playButton}>
            <svg 
              width="68" 
              height="48" 
              viewBox="0 0 68 48" 
              fill="none" 
              xmlns="http://www.w3.org/2000/svg"
            >
              <path 
                d="M66.52 7.74c-.78-2.93-2.49-5.41-5.42-6.19C55.79.13 34 0 34 0S12.21.13 6.9 1.55c-2.93.78-4.63 3.26-5.42 6.19C.06 13.05 0 24 0 24s.06 10.95 1.48 16.26c.78 2.93 2.49 5.41 5.42 6.19C12.21 47.87 34 48 34 48s21.79-.13 27.1-1.55c2.93-.78 4.64-3.26 5.42-6.19C67.94 34.95 68 24 68 24s-.06-10.95-1.48-16.26z" 
                fill="red"
              />
              <path d="M45 24 27 14v20l18-10z" fill="white"/>
            </svg>
          </div>
        </div>
      ) : (
        <iframe
          src={embedUrl}
          title={title}
          width="100%"
          height="100%"
          frameBorder="0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowFullScreen
          className={styles.iframe}
        />
      )}
    </div>
  );
};

export default YouTubeVideo;
