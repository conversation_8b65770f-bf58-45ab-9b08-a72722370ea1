
/* FIRST FRAME - Blue background with logos and characters */
.firstFrame {
  background: linear-gradient(180deg, #2850FF 0%, #FFF 93.45%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 40px 20px;
}

.logoX{
  color: white;
  font-weight: bolder;
  font-size: 3rem;
}

.headerLogo {
     display: flex;
    gap: 1rem;
    justify-content: center;
    align-items: center;
    margin-bottom: 2rem;
}

.charactersSection {
  display: flex;
  justify-content: center;
  align-items: center;
}


/* SECOND FRAME - White background with text */
.secondFrame {
  background: #FFFFFF;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 80px 20px;
  text-align: left;
}

.mainHeading {
  font-size: 4rem;
  font-weight: 700;
  font-family: var(--font-poppins);
  color: #EA73C0;
  margin: 0;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  text-align: left;
}

.subHeading {
  font-size: 1.8rem;
  font-family: var(--font-poppins);
  margin: 0;
  color: #949494;
  margin-bottom: 30px;
  text-align: left;
  font-weight: 500;
}

.description {
  font-size: 1.4rem;
  color: #86868B;
  font-family: var(--font-poppins);
  line-height: 1.8;
  margin: 0;
  width: 100%;
  text-align: left;
  font-weight: 600;
}

/* Trailer Video Section */
.trailerHeading {
  font-size: 3rem;
  font-weight: 700;
  font-family: var(--font-poppins);
  color: #2850FF;
  margin: 40px 0 20px 0;
  text-align: center;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.trailerVideoContainer {
  width: 100%;
  max-width: 800px;
  margin: 0 auto 40px auto;
  padding: 0 20px;
}

.trailerVideo {
  width: 100%;
  height: 450px;
}

/* Teaser Videos Section */
.teaserHeading {
  font-size: 3rem;
  font-weight: 700;
  font-family: var(--font-poppins);
  color: #EA73C0;
  margin: 40px 0 20px 0;
  text-align: center;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.teaserVideosContainer {
  display: flex;
  gap: 20px;
  padding: 0 20px;
  width: max-content;
  min-width: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.teaserVideosContainer::-webkit-scrollbar {
  display: none;
}

.teaserVideoCard {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  width: 320px;
  height: 240px;
  transition: transform 0.3s ease;
  flex-shrink: 0;
}

.teaserVideoCard:hover {
  transform: translateY(-5px);
}

.teaserVideo {
  width: 100%;
  height: 100%;
}

.teaserVideoOverlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.8) 100%);
  padding: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
}

.teaserVideoText {
  font-size: 1rem;
  font-weight: 900;
  color: #FFFFFF;
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

/* THIRD FRAME - Game screenshots */
.thirdFrame {
  background: #F8F9FA;
  padding: 80px 0;
  overflow-x: auto;
  overflow-y: hidden;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
  -ms-overflow-style: none; 
}

.gamesContainer {
  display: flex;
  gap: 20px;
  padding: 0 20px;
  width: max-content;
  min-width: 100%;
}

.gameCard {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  width: 320px;
  height: 240px;
  transition: transform 0.3s ease;
  flex-shrink: 0;
}

.gameCard:hover {
  transform: translateY(-5px);
}

.gameImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.gameOverlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.8) 100%);
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.overlayText {
  font-size: 1.1rem;
  font-weight: 900;
  color: #FFFFFF;
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

/* Placeholder for missing images */
.placeholderImage {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 1.2rem;
  font-weight: bold;
  text-align: center;
}

.placeholderLogo {
  width: 100%;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
  margin-bottom: 40px;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .gamesContainer {
    padding: 0 15px;
  }

  .gameCard {
    width: 300px;
    height: 225px;
  }

  .teaserVideosContainer {
    padding: 0 15px;
  }

  .teaserVideoCard {
    width: 300px;
    height: 225px;
  }

  .trailerVideo {
    height: 400px;
  }
}

@media (max-width: 768px) {
  .firstFrame {
    padding: 30px 15px;
    min-height: 60vh;
  }

  .logoImage {
    max-width: 400px;
  }

  .charactersImage {
    max-width: 600px;
  }

  .mainHeading {
    font-size: 2.5rem;
  }

  .subHeading {
    font-size: 1.4rem;
  }

  .description {
    font-size: 1.2rem;
  }

  .trailerHeading,
  .teaserHeading {
    font-size: 2.5rem;
  }

  .trailerVideo {
    height: 300px;
  }

  .secondFrame {
    padding: 60px 20px;
  }

  .thirdFrame {
    padding: 60px 0;
  }

  .gamesContainer,
  .teaserVideosContainer {
    gap: 15px;
    padding: 0 15px;
  }

  .gameCard,
  .teaserVideoCard {
    width: 280px;
    height: 210px;
  }

  .placeholderLogoText {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .mainHeading {
    font-size: 2rem;
  }

  .subHeading {
    font-size: 1.2rem;
  }

  .description {
    font-size: 1rem;
  }

  .trailerHeading,
  .teaserHeading {
    font-size: 2rem;
  }

  .trailerVideo {
    height: 250px;
  }

  .gamesContainer,
  .teaserVideosContainer {
    padding: 0 10px;
  }

  .gameCard,
  .teaserVideoCard {
    width: 250px;
    height: 190px;
  }

  .teaserVideoText {
    font-size: 0.9rem;
  }

  .placeholderCharacters {
    height: 250px;
    font-size: 1.2rem;
  }
}