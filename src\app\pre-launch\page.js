"use client";
import StoreIcons from "@/components/Footer/StoreIcons";
import FormCtaButton from "@/components/FormCtaButton";
import { useTranslations } from "next-intl";
import Head from "next/head";
import Image from "next/image";
import styles from "./styles.module.css";

const SmurfsComingSoon = () => {
  const gameCards = [
    { id: 1, text: "THE CHARACTERS YOU LOVE!" },
    { id: 2, text: "WATCH OUT FOR GARGAMEL!" },
    { id: 3, text: "玩迷你游戏" },
    { id: 4, text: "MAGICAL ADVENTURES!" },
    { id: 5, text: "LEARN WITH SMURFS!" },
    { id: 6, text: "SMURF VILLAGE!" },
  ];

  const t = useTranslations("Footer");

  const icons = [
    {
      src: "/images/footer/socialIcons/Amazon.webp",
      alt: t("Amazon"),
      onClick: () => window.open("https://www.amazon.com/gp/product/B0DH6RT2JV", "_blank"),
    },
    {
      src: "/images/footer/socialIcons/Appstore.webp",
      alt: t("Appstore"),
      onClick: () =>
        window.open(
          "https://apps.apple.com/us/app/skidos-learning-games-for-kids/id1483744837",
          "_blank"
        ),
    },
    {
      src: "/images/footer/socialIcons/Playstore.webp",
      alt: t("Playstore"),
      onClick: () =>
        window.open(
          "https://play.google.com/store/apps/details?id=skidos.shopping.toddler.learning.games&hl=en_IN",
          "_blank"
        ),
    },
  ];

  return (
    <>
      <Head>
        <title>SKIDOS x Smurfs</title>
        <meta
          name="description"
          content="Smurfs Are Here to Learn & Play! SKIDOS and the Smurfs have teamed up to bring exciting educational games."
        />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      </Head>

      <>
        {/* First Frame - Blue background with logos and characters */}
        <div className={styles.firstFrame}>
          {/* Logo placeholder - replace with actual image */}
          <div className={styles.headerLogo}>
            <Image src="/images/skidosLogo.png" width={300} height={100} alt="SKIDOS Logo" />
            <div className={styles.logoX}>X</div>
            <Image
              src="/images/skidos-smurfs/smurf-logo.png"
              width={300}
              height={100}
              alt="SKIDOS Logo"
            />
          </div>

          {/* Characters section */}
          <div className={styles.charactersSection}>
            {/* Character placeholder - replace with actual smurf banner image */}
            <Image
              src="/images/skidos-smurfs/smurf-banner.png"
              width={1000}
              height={500}
              alt="Smurf Characters"
            />
          </div>
        </div>

        {/* Second Frame - White background with text */}
        <div className={styles.secondFrame}>
          <h1 className={styles.mainHeading}>Smurfs Are Here to Learn & Play!</h1>
          <p className={styles.subHeading}>(Smurf x SKIDOS is coming soon)</p>
          <p className={styles.description}>
            Get ready for a magical adventure in learning! SKIDOS and the Smurfs have teamed up to
            bring your kids exciting educational games with their favorite blue buddies. Fun meets
            learning like never before!
          </p>
          <h1>Watch the trailer now</h1>
          <div>
            <Image
              src="/images/skidos-smurfs/smurf-banner.png"
              width={1000}
              height={500}
              alt="Smurf Characters"
            />
          </div>
          <h1>Teaser</h1>
          <div className={styles.gamesContainer}>
            {gameCards.map((card) => (
              <div key={card.id} className={styles.gameCard}>
                <div className={styles.placeholderImage}>Game Screenshot {card.id}</div>
                <div className={styles.gameOverlay}>
                  <span className={styles.overlayText}>{card.text}</span>
                </div>
              </div>
            ))}
          </div>
          <FormCtaButton text="Notify Me" />
          <FormCtaButton text="Download" />
          <StoreIcons icons={icons} />
        </div>

        {/* Third Frame - Game screenshots */}
        <div className={styles.thirdFrame}>
          <div className={styles.gamesContainer}>
            {gameCards.map((card) => (
              <div key={card.id} className={styles.gameCard}>
                <div className={styles.placeholderImage}>Game Screenshot {card.id}</div>
                <div className={styles.gameOverlay}>
                  <span className={styles.overlayText}>{card.text}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </>
    </>
  );
};

export default SmurfsComingSoon;
