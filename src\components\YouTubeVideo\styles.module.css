.videoContainer {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  background: #000;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.thumbnailContainer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;
}

.thumbnailContainer:hover {
  transform: scale(1.02);
}

.thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.playButton {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transition: transform 0.3s ease;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

.thumbnailContainer:hover .playButton {
  transform: translate(-50%, -50%) scale(1.1);
}

.iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
  border-radius: 12px;
}

.errorContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border: 2px dashed #ccc;
  border-radius: 12px;
  padding: 2rem;
  color: #666;
  font-family: var(--font-poppins);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .videoContainer {
    border-radius: 8px;
  }
  
  .playButton svg {
    width: 48px;
    height: 36px;
  }
}

@media (max-width: 480px) {
  .videoContainer {
    border-radius: 6px;
  }
  
  .playButton svg {
    width: 40px;
    height: 30px;
  }
}
